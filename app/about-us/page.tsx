"use client"

import { useState } from "react"
import { <PERSON><PERSON>eader } from "@/components/page-header"
import { <PERSON><PERSON> } from "@/components/ui/button"
import Image from "next/image"
import { Linkedin } from "lucide-react"

export default function AboutUsPage() {
  const [flippedCards, setFlippedCards] = useState<string[]>([])

  const toggleCard = (memberName: string) => {
    setFlippedCards(prev =>
      prev.includes(memberName)
        ? prev.filter(name => name !== memberName)
        : [...prev, memberName]
    )
  }



  const teamMembers = [
    {
      name: "<PERSON><PERSON>",
      position: "Co-Founder",
      image: "https://hebbkx1anhila5yf.public.blob.vercel-storage.com/headshot-r2koYBigCOYP9UVZeK8ifY8FxKm7BH.jpeg",
      bio: "<PERSON><PERSON>, Co-founder and CEO of Fuse.vip. Bringing a degree in Communication with a concentration in Media Studies from Cal St. San Bernardino. <PERSON><PERSON> combines his expertise in digital marketing, technology, and customer engagement to revolutionize loyalty programs. His passion for innovation and commitment to creating value for businesses drives the company's mission forward. Under his leadership, Fuse.vip is pioneering the integration of blockchain with traditional loyalty systems to create more meaningful customer connections.",
      linkedin: "https://www.linkedin.com/in/raul-salazar-6b9967165",
    },

    {
      name: "Dylan Silver",
      position: "Co-founder",
      image: "/images/dylan-silver.jpg",
      bio: "<PERSON> Silver, Co-founder and CSO of Fuse.vip, leads Business Development and Sales for Fuse.vip. Leveraging his extensive network in the health and fitness industry to drive strategic partnerships and platform growth. A graduate of Cal State San Bernardino with a Bachelor's degree in Kinesiology, Dylan's deep understanding of human performance, health trends, and community dynamics positions him to identify high-value opportunities for Fuse.vip's expansion. His strong relationships within the healthcare, fitness, and wellness sectors allow Fuse.vip to carve out a powerful niche in loyalty-driven commerce. Dylan's entrepreneurial mindset and ability to build meaningful partnerships have been instrumental in establishing Fuse.vip's early client network and laying the foundation for long-term growth in the digital loyalty space.",
      linkedin: "https://www.linkedin.com/in/dylan-silver-028077354/",
    },
  ]

  return (
    <>
      <PageHeader
        title="About Us"
        subtitle="OUR TEAM"
        description="Meet the passionate team behind Fuse.vip and learn about our mission to revolutionize loyalty programs."
      />

      {/* Art Showcase Section */}
      <section className="py-16 bg-gradient-to-b from-gray-50 to-white">
        <div className="container mx-auto px-4">
          {/* Hero Banner with YouTube-sized art */}
          <div className="relative mb-12 rounded-2xl overflow-hidden shadow-2xl">
            <div className="relative h-64 md:h-80 lg:h-96">
              <Image
                src="/images/hero-banner-art.jpeg"
                alt="Fuse.vip Creative Vision"
                fill
                className="object-cover"
                priority
              />
              <div className="absolute inset-0 bg-gradient-to-r from-black/60 via-black/30 to-transparent" />
              <div className="absolute inset-0 flex items-center">
                <div className="container mx-auto px-8">
                  <div className="max-w-2xl">
                    <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-white mb-4">
                      Our Creative Vision
                    </h2>
                    <p className="text-lg md:text-xl text-white/90 leading-relaxed">
                      Fusing innovation with artistry to create meaningful connections between businesses and their communities.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Featured Square Art */}
          <div className="flex justify-center mb-16">
            <div className="bg-white rounded-2xl shadow-xl overflow-hidden max-w-md">
              <div className="relative aspect-square">
                <Image
                  src="/images/featured-art-square.jpeg"
                  alt="Fuse.vip Brand Art"
                  fill
                  className="object-cover"
                />
              </div>
              <div className="p-6">
                <h3 className="text-xl font-bold text-gray-900 mb-2">Brand Identity</h3>
                <p className="text-gray-600">
                  Every element of our visual identity reflects our commitment to innovation,
                  community, and the seamless fusion of technology with human connection.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Symbolic Declaration Section */}
      <section className="py-20 bg-gradient-to-b from-slate-900 via-slate-800 to-slate-900 relative overflow-hidden">
        {/* Subtle patriotic accent elements */}
        <div className="absolute inset-0 opacity-5">
          <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-red-500 via-white to-blue-500"></div>
          <div className="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-red-500 via-white to-blue-500"></div>
        </div>

        <div className="container mx-auto px-4 relative z-10">
          <div className="max-w-4xl mx-auto text-center">
            {/* Poster Display */}
            <div className="mb-12">
              <div className="relative inline-block">
                <div className="relative w-80 h-80 md:w-96 md:h-96 mx-auto rounded-lg overflow-hidden shadow-2xl border-4 border-amber-400/30">
                  <Image
                    src="/images/fallout-fuse-poster.png"
                    alt="Fuse Declaration of Financial Independence"
                    fill
                    className="object-cover"
                  />
                </div>
                {/* Subtle glow effect */}
                <div className="absolute inset-0 rounded-lg bg-gradient-to-t from-amber-400/20 to-transparent pointer-events-none"></div>
              </div>
            </div>

            {/* Declaration Text */}
            <div className="space-y-6">
              <h3 className="text-3xl md:text-4xl font-bold text-white mb-6 tracking-wide">
                24/7 Support is available
              </h3>

              <div className="max-w-2xl mx-auto">
                <p className="text-xl md:text-2xl text-amber-100 font-semibold leading-relaxed">
                  American-Built Financial Freedom.
                </p>
                <div className="flex items-center justify-center my-4">
                  <div className="h-px bg-amber-400/50 flex-1 max-w-16"></div>
                  <div className="px-4 text-amber-400 text-lg">—</div>
                  <div className="h-px bg-amber-400/50 flex-1 max-w-16"></div>
                </div>
                <p className="text-lg md:text-xl text-white/90 italic">
                  Every Transaction is a Declaration of Independence
                </p>
              </div>
            </div>

            {/* Subtle decorative elements */}
            <div className="mt-12 flex justify-center space-x-8 opacity-30">
              <div className="w-2 h-2 bg-red-500 rounded-full"></div>
              <div className="w-2 h-2 bg-white rounded-full"></div>
              <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
            </div>
          </div>
        </div>
      </section>

      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">Meet Our Team</h2>
            <p className="text-[#4a4a4a] max-w-2xl mx-auto">
              Fuse's united network of experts brings together a wealth of experience in blockchain, loyalty programs, and
              customer engagement.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-10">
            {teamMembers.map((member) => {
              const isFlipped = flippedCards.includes(member.name)
              return (
                <div key={member.name} className="max-w-sm mx-auto">
                  {!isFlipped ? (
                    // Front of Card
                    <div className="bg-white rounded-xl shadow-lg overflow-hidden">
                      <div className="h-80 overflow-hidden bg-gray-50">
                        <Image
                          src={member.image || "/placeholder.svg"}
                          alt={member.name}
                          width={400}
                          height={320}
                          className="w-full h-full object-cover object-top"
                        />
                      </div>
                      <div className="p-8">
                        <div className="flex justify-between items-center mb-2">
                          <h3 className="font-bold text-2xl">{member.name}</h3>
                          {member.linkedin && (
                            <a
                              href={member.linkedin}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="text-[#0A66C2] hover:text-[#0A66C2]/80 transition-colors"
                              aria-label={`${member.name}'s LinkedIn profile`}
                            >
                              <Linkedin className="h-6 w-6" />
                            </a>
                          )}
                        </div>
                        <p className="text-[#3A56FF] mb-6 font-semibold text-lg">{member.position}</p>
                        <p className="text-[#4a4a4a] text-sm mb-6">
                          {member.bio.split(' ').slice(0, 15).join(' ')}...
                        </p>
                        <Button
                          onClick={() => toggleCard(member.name)}
                          className="w-full bg-[#3A56FF] hover:bg-[#3A56FF]/90 text-white"
                        >
                          More Info
                        </Button>
                      </div>
                    </div>
                  ) : (
                    // Back of Card
                    <div className="bg-gradient-to-br from-[#3A56FF] to-[#2A46EF] rounded-xl shadow-lg overflow-hidden text-white h-[600px]">
                      <div className="p-8 h-full flex flex-col">
                        <div className="flex justify-between items-center mb-4">
                          <h3 className="font-bold text-2xl">{member.name}</h3>
                          {member.linkedin && (
                            <a
                              href={member.linkedin}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="text-white hover:text-white/80 transition-colors"
                              aria-label={`${member.name}'s LinkedIn profile`}
                            >
                              <Linkedin className="h-6 w-6" />
                            </a>
                          )}
                        </div>
                        <p className="text-white/90 mb-6 font-semibold text-lg">{member.position}</p>
                        <div className="flex-1 overflow-y-auto">
                          <p className="text-white/90 text-sm leading-relaxed">{member.bio}</p>
                        </div>
                        <Button
                          onClick={() => toggleCard(member.name)}
                          variant="outline"
                          className="w-full mt-6 border-white text-white hover:bg-white hover:text-[#3A56FF]"
                        >
                          Back to Photo
                        </Button>
                      </div>
                    </div>
                  )}
                </div>
              )
            })}
          </div>
        </div>
      </section>


    </>
  )
}
