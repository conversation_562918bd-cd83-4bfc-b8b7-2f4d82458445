"use client"

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { ExternalLink, Wallet, CheckCircle, AlertCircle, Copy, Smartphone, Monitor, QrCode } from 'lucide-react'
import { toast } from 'sonner'
import { useXummClient } from '@/hooks/use-xumm-client'
import { QRCodeSVG as QRCode } from 'qrcode.react'

interface TrustlineSetupProps {
  className?: string
}

export function TrustlineSetup({ className }: TrustlineSetupProps) {
  const [copied, setCopied] = useState(false)
  const [showClientMethod, setShowClientMethod] = useState(false)
  const [clientQRData, setClientQRData] = useState<string | null>(null)
  const [isCreatingPayload, setIsCreatingPayload] = useState(false)
  
  const { 
    isConnected, 
    account,
    sdkReady,
    createPayload,
    isMobile
  } = useXummClient()

  // FUSE token trustline configuration
  const trustlineConfig = {
    currency: '4655534500000000000000000000000000000000', // 'FUSE' padded to 20 bytes hex
    issuer: 'rs2G9J95qwL3yw241JTRdgms2hhcLouVHo',
    limit: '********.********'
  }

  // Generate Xaman deep link for trustline setup (optimized for URL length)
  const generateXamanLink = () => {
    try {
      const tx = {
        TransactionType: 'TrustSet',
        LimitAmount: {
          currency: trustlineConfig.currency,
          issuer: trustlineConfig.issuer,
          value: trustlineConfig.limit,
        }
      }

      // Use compact JSON formatting to reduce URL length
      const str = JSON.stringify(tx, null, 0)
      const hex = Buffer.from(str, 'utf-8').toString('hex')
      return `https://xaman.app/detect/${hex}`
    } catch (error) {
      console.error('Error generating Xaman link:', error)
      toast.error('Failed to generate Xaman link. Please try XRPL Services instead.')
      return ''
    }
  }

  const handleXamanClick = () => {
    const link = generateXamanLink()
    window.open(link, '_blank')
    toast.success('Opening Xaman app... Complete the trustline setup in your wallet!')
  }

  // XRPL Services link for manual setup
  const xrplServicesLink = `https://xrpl.services/?issuer=${trustlineConfig.issuer}&currency=${trustlineConfig.currency}&limit=${trustlineConfig.limit}`

  const copyToClipboard = (text: string, label: string) => {
    navigator.clipboard.writeText(text)
    setCopied(true)
    toast.success(`${label} copied to clipboard!`)
    setTimeout(() => setCopied(false), 2000)
  }

  // Client-side trustline creation using Xumm SDK
  const handleClientSideTrustline = async () => {
    if (!sdkReady) {
      toast.error('Xumm SDK not ready. Please try again.')
      return
    }

    try {
      setIsCreatingPayload(true)
      
      const payload = {
        TransactionType: 'TrustSet',
        LimitAmount: {
          currency: trustlineConfig.currency,
          issuer: trustlineConfig.issuer,
          value: trustlineConfig.limit,
        }
      }

      // For desktop users, we'd typically get a QR code
      // For mobile users, it would deeplink directly
      if (!isMobile()) {
        // Generate QR data for desktop scanning
        const qrData = `xumm://payload/${JSON.stringify(payload)}`
        setClientQRData(qrData)
        toast.success('QR code generated! Scan with your Xumm mobile app.')
      }

      // Create the payload using client SDK
      const result = await createPayload(payload)
      
      if (result) {
        toast.success('Trustline setup completed successfully!')
        setShowClientMethod(false)
        setClientQRData(null)
      }
      
    } catch (error) {
      console.error('Client-side trustline creation failed:', error)
      toast.error(`Failed to create trustline: ${error instanceof Error ? error.message : 'Unknown error'}`)
    } finally {
      setIsCreatingPayload(false)
    }
  }

  return (
    <Card className={`bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-950/50 dark:to-indigo-950/50 border-blue-200 dark:border-blue-800 ${className}`}>
      <CardHeader className="text-center">
        <div className="mx-auto w-16 h-16 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full flex items-center justify-center mb-4">
          <Wallet className="w-8 h-8 text-white" />
        </div>
        <CardTitle className="text-2xl font-bold text-gray-900 dark:text-white">
          Set FUSE Trustline
        </CardTitle>
        <CardDescription className="text-gray-600 dark:text-gray-300">
          Enable your XRP wallet to hold $FUSE tokens by setting up a trustline
        </CardDescription>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* What is a Trustline */}
        <div className="bg-blue-100/50 dark:bg-blue-900/30 rounded-lg p-4 border border-blue-200 dark:border-blue-700">
          <div className="flex items-start gap-3">
            <AlertCircle className="w-5 h-5 text-blue-600 dark:text-blue-400 mt-0.5 flex-shrink-0" />
            <div>
              <h4 className="font-semibold text-blue-900 dark:text-blue-100 mb-1">
                What is a Trustline?
              </h4>
              <p className="text-sm text-blue-800 dark:text-blue-200">
                A trustline tells your XRP wallet that you trust the FUSE token issuer and want to hold FUSE tokens. 
                This is a one-time setup required before you can receive or trade FUSE tokens.
              </p>
            </div>
          </div>
        </div>

        {/* Token Information */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <label className="text-sm font-medium text-gray-700 dark:text-gray-300">Token</label>
            <div className="flex items-center gap-2 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
              <span className="font-mono text-lg font-bold text-blue-600 dark:text-blue-400">$FUSE</span>
            </div>
          </div>
          <div className="space-y-2">
            <label className="text-sm font-medium text-gray-700 dark:text-gray-300">Trust Limit</label>
            <div className="flex items-center gap-2 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
              <span className="font-mono text-sm">{trustlineConfig.limit}</span>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => copyToClipboard(trustlineConfig.limit, 'Trust limit')}
                className="h-6 w-6 p-0"
              >
                <Copy className="w-3 h-3" />
              </Button>
            </div>
          </div>
        </div>

        {/* Issuer Address */}
        <div className="space-y-2">
          <label className="text-sm font-medium text-gray-700 dark:text-gray-300">Issuer Address</label>
          <div className="flex items-center gap-2 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
            <span className="font-mono text-sm flex-1 break-all">{trustlineConfig.issuer}</span>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => copyToClipboard(trustlineConfig.issuer, 'Issuer address')}
              className="h-6 w-6 p-0 flex-shrink-0"
            >
              <Copy className="w-3 h-3" />
            </Button>
          </div>
        </div>

        {/* Setup Methods */}
        <div className="space-y-4">
          <h4 className="font-semibold text-gray-900 dark:text-white">Choose Your Setup Method:</h4>
          
          {/* Method 0: Client-Side SDK (New) */}
          {sdkReady && (
            <div className="border-2 border-green-200 dark:border-green-700 rounded-lg p-4 bg-green-50/30 dark:bg-green-950/20">
              <div className="flex items-center gap-3 mb-3">
                <QrCode className="w-5 h-5 text-green-600" />
                <h5 className="font-semibold text-gray-900 dark:text-white">Client-Side SDK: Direct Browser Connection</h5>
                <span className="px-2 py-1 text-xs bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 rounded-full">
                  Latest
                </span>
              </div>
              <p className="text-sm text-gray-600 dark:text-gray-300 mb-4">
                {isMobile() 
                  ? "Direct wallet interaction - creates trustline via your mobile browser without server dependency."
                  : "Direct wallet interaction - scan QR code for instant trustline setup without backend load."
                }
              </p>
              <div className="text-xs text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-950/30 p-2 rounded border border-blue-200 dark:border-blue-800 mb-4">
                💡 This method handles transactions client-side, protecting our backend while giving you direct wallet control
              </div>
              
              {!showClientMethod ? (
                <Button
                  onClick={() => setShowClientMethod(true)}
                  className="w-full bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white"
                >
                  {isMobile() ? (
                    <Smartphone className="w-4 h-4 mr-2" />
                  ) : (
                    <Monitor className="w-4 h-4 mr-2" />
                  )}
                  Use Browser Connection
                </Button>
              ) : (
                <div className="space-y-4">
                  {/* QR Code for desktop */}
                  {clientQRData && !isMobile() && (
                    <div className="flex flex-col items-center space-y-3 p-4 border rounded-lg bg-white dark:bg-gray-900">
                      <QRCode 
                        value={clientQRData} 
                        size={200}
                        level="M"
                        style={{ padding: 10 }}
                      />
                      <p className="text-sm text-center text-gray-600 dark:text-gray-400">
                        Scan this QR code with your Xumm mobile app
                      </p>
                    </div>
                  )}
                  
                  <div className="flex gap-2">
                    <Button
                      onClick={handleClientSideTrustline}
                      disabled={isCreatingPayload}
                      className="flex-1 bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white"
                    >
                      {isCreatingPayload ? (
                        <>
                          <div className="w-4 h-4 mr-2 border-2 border-white border-t-transparent rounded-full animate-spin" />
                          Creating...
                        </>
                      ) : (
                        <>
                          <Wallet className="w-4 h-4 mr-2" />
                          {isMobile() ? 'Open Xumm App' : 'Generate QR Code'}
                        </>
                      )}
                    </Button>
                    <Button
                      variant="outline"
                      onClick={() => {
                        setShowClientMethod(false)
                        setClientQRData(null)
                      }}
                      disabled={isCreatingPayload}
                    >
                      Cancel
                    </Button>
                  </div>
                </div>
              )}
            </div>
          )}
          
          {/* Method 1: Xaman App */}
          <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
            <div className="flex items-center gap-3 mb-3">
              <CheckCircle className="w-5 h-5 text-green-600" />
              <h5 className="font-semibold text-gray-900 dark:text-white">External: Xaman Deep Link</h5>
            </div>
            <p className="text-sm text-gray-600 dark:text-gray-300 mb-4">
              One-click trustline setup using the Xaman mobile app. This is the easiest and most secure method.
            </p>
            <Button
              onClick={handleXamanClick}
              className="w-full bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white"
            >
              <Wallet className="w-4 h-4 mr-2" />
              Open in Xaman App
              <ExternalLink className="w-4 h-4 ml-2" />
            </Button>
          </div>

          {/* Method 2: XRPL Services */}
          <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
            <div className="flex items-center gap-3 mb-3">
              <CheckCircle className="w-5 h-5 text-green-600" />
              <h5 className="font-semibold text-gray-900 dark:text-white">External: XRPL Services</h5>
            </div>
            <p className="text-sm text-gray-600 dark:text-gray-300 mb-4">
              Use XRPL Services web interface to set up the trustline manually. Good for desktop users.
            </p>
            <Button
              onClick={() => window.open(xrplServicesLink, '_blank')}
              variant="outline"
              className="w-full border-blue-200 dark:border-blue-700 text-blue-600 dark:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-950"
            >
              <ExternalLink className="w-4 h-4 mr-2" />
              Open XRPL Services
            </Button>
          </div>
        </div>

        {/* Quick Links for Advanced Users */}
        <div className="bg-gray-50 dark:bg-gray-900/50 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
          <h4 className="font-semibold text-gray-900 dark:text-white mb-3">Quick Links (Advanced Users):</h4>
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <span className="text-sm text-gray-600 dark:text-gray-400 min-w-[80px]">Xaman:</span>
              <code className="text-xs bg-gray-100 dark:bg-gray-800 px-2 py-1 rounded flex-1 break-all">
                {generateXamanLink()}
              </code>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => copyToClipboard(generateXamanLink(), 'Xaman link')}
                className="h-6 w-6 p-0 flex-shrink-0"
              >
                <Copy className="w-3 h-3" />
              </Button>
            </div>
            <div className="flex items-center gap-2">
              <span className="text-sm text-gray-600 dark:text-gray-400 min-w-[80px]">XRPL:</span>
              <code className="text-xs bg-gray-100 dark:bg-gray-800 px-2 py-1 rounded flex-1 break-all">
                {xrplServicesLink}
              </code>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => copyToClipboard(xrplServicesLink, 'XRPL Services link')}
                className="h-6 w-6 p-0 flex-shrink-0"
              >
                <Copy className="w-3 h-3" />
              </Button>
            </div>
          </div>
        </div>

        {/* Important Notes */}
        <div className="bg-amber-50 dark:bg-amber-950/30 rounded-lg p-4 border border-amber-200 dark:border-amber-800">
          <div className="flex items-start gap-3">
            <AlertCircle className="w-5 h-5 text-amber-600 dark:text-amber-400 mt-0.5 flex-shrink-0" />
            <div>
              <h4 className="font-semibold text-amber-900 dark:text-amber-100 mb-2">Important Notes:</h4>
              <ul className="text-sm text-amber-800 dark:text-amber-200 space-y-1">
                <li>• You need at least 2 XRP in your wallet to set up a trustline</li>
                <li>• Setting a trustline is a one-time process per token</li>
                <li>• Once set, you can receive and trade FUSE tokens</li>
                <li>• The trustline can be removed later if needed</li>
              </ul>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
